import React, { type FC } from 'react';
import { useMessage } from '@messageformat/react';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import { CheckCircleIcon, ChevronRightIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import { Link } from 'react-router';

export type ProjectSetupProgressProps = {
  options: {
    label: string;
    href: string;
    completed: boolean;
  }[];
  percentage: number;
};

export const ProjectSetupProgress: FC<ProjectSetupProgressProps> = ({ options, percentage }) => {
  const progressLabel = useMessage('projectSetup.label');

  return (
    <Dropdown.Root>
      <Dropdown.Trigger className="w-full" aria-label="project setup progress">
        <ProgressBar.Root aria-label="project progress" progress={percentage} color="primary">
          <ProgressBar.Header showProgress>
            <ProgressBar.Title className="text-white">{progressLabel}</ProgressBar.Title>
          </ProgressBar.Header>
        </ProgressBar.Root>
      </Dropdown.Trigger>
      <Dropdown.Items>
        {options.map(({ label, href, completed }) => {
          if (completed)
            return (
              <Dropdown.Item
                key={label}
                aria-label={label}
                disabled
                endAdornment={<CheckCircleIcon className="h-4 w-4 text-green-500" />}
              >
                {label}
              </Dropdown.Item>
            );

          return (
            <Link to={href} key={label} aria-label={label}>
              <Dropdown.Item endAdornment={<ChevronRightIcon className="h-4 w-4 text-gray-700" />}>
                {label}
              </Dropdown.Item>
            </Link>
          );
        })}
      </Dropdown.Items>
    </Dropdown.Root>
  );
};
